## jsx语法

### 什么是jsx

jsx是一种JavaScript的语法扩展，它看起来像是模板语言，但可以在js中直接使用。jsx是React的核心，用于描述UI的外观。

### jsx的优点

- jsx代码更加直观，更像是模板语言，有利于理解和编写
- jsx代码更加简洁，减少了大量的createElement代码
- jsx代码更加强大，可以直接在js中使用html标签

### jsx的基本语法

#### 使用表达式

```jsx
import React from 'react';
import ReactDOM from 'react-dom';

const name = '<PERSON>';
const element = <h1>Hello, {name}</h1>;

ReactDOM.render(
  element,
  document.getElementById('root')
);
```

#### 定义组件

```jsx
import React from 'react';

function Welcome(props) {
  return <h1>Hello, {props.name}</h1>;
}

const element = <Welcome name="Sara" />;
```

注意：组件的名称必须以大写字母开头。

#### jsx中使用html标签

```jsx
import React from 'react';

const element = (
  <div>
    <h1>Hello!</h1>
    <h2>Good to see you here.</h2>
  </div>
);
```

注意：jsx中的标签必须闭合。换行需要用括号包裹。

### jsx的注意事项

- jsx中的标签必须闭合
- jsx中的标签必须小写
- jsx中的标签必须有一个根元素
- 永远不要在组件中嵌套定义组件


