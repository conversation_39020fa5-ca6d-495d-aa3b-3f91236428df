# proto buffer

## syntax

声明proto文件的语法版本，proto3是最新的版本，proto2已经不再推荐使用。

```proto
syntax = "proto3";
```

## package

声明proto文件的包名，一般是项目的包名。

```proto
package com.example.proto;
```

## import

导入其他proto文件，可以使用相对路径，也可以使用绝对路径。

```proto
import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";
```

## option

默认值，可以用来设置类型的默认值。也可以用来设置java的包名。

```proto
option java_package = "com.example.proto";

option (google.protobuf.Duration).java_package = "com.example.proto";
```

## repeated

重复的，可以用来表示数组。

```proto
repeated int32 id = 1;
```

