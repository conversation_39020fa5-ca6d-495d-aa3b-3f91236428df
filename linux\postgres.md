# postgres命令行使用
## 连接数据库

```shell
psql -h localhost -p 5432 -U postgres -d postgres
```
## 查看数据库

```shell
\l
```
## 进入数据库

```shell
\c database_name
```

## 查看表

```shell
\dt
```

## 查看表结构

```shell
\d table_name
```

## 查看表数据

```shell
select * from table_name;
```

## 退出数据库

```shell
\q
```

## 查看用户

```shell
\du
```

## 查看用户权限

```shell
\dp
```

## 创建用户

```shell
create user username with password 'password';
```

## 删除用户

```shell
drop user username;
```

## 授权用户

```shell
grant all privileges on database_name to username;
```

## 查看数据库大小

```shell
select pg_size_pretty(pg_database_size('database_name'));
```

## 查看表大小

```shell
select pg_size_pretty(pg_total_relation_size('table_name'));
```

## 查看表索引大小

```shell
select pg_size_pretty(pg_indexes_size('table_name'));
```

## 创建数据库

```shell
create database database_name;
```

## 删除数据库

```shell
drop database database_name;
```

## 备份数据库

```shell
pg_dump -h localhost -p 5432 -U postgres -d database_name -f database_name.sql
```

## 恢复数据库

```shell
psql -h localhost -p 5432 -U postgres -d database_name -f database_name.sql
```

## 清空数据库
    
```shell
drop schema public cascade;
create schema public;
```

## 查看数据库版本

```shell
select version();
```

## 查看数据库连接数

```shell
select count(*) from pg_stat_activity;
```

## 查看数据库连接信息

```shell
select * from pg_stat_activity;
```

## 执行SQL文件

```shell
psql -h localhost -p 5432 -U postgres -d database_name -f sql_file.sql
```

## 如何修复表
问题：ERROR:  missing chunk number 0 for toast value 98076 in pg_toast_23172
解决方法：
```shell
select reltoastrelid::regclass from pg_class where relname = 'pg_toast_23172';
REINDEX TABLE pg_toast.pg_toast_23172;
REINDEX TABLE sys_page_codes;
VACUUM ANALYSE sys_page_codes;
select count(*) from mytable;
## 二分法查找定位错误的数据
select * from mytable order by id limit 5000 offset 0;
select * from mytable order by id limit 5000 offset 5000;
select * from mytable order by id limit 5000 offset 10000;
select * from mytable order by id limit 5000 offset 15000;
select * from mytable order by id limit 5000 offset 20000;
## 找到那条数据，获取依次获取字段,定位到错误的字段
select id from mytable order by id limit 1 offset 25000;
select id,code,name from mytable where id = '12323';
## 修复错误的字段
update mytable set code = nil where id = '12323';
## 从备份数据库中找到该数据，恢复
```
