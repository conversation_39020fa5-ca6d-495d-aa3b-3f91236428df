# 权限管理

## root权限

### 修改root密码

```shell
sudo passwd root
```

### 切换到root用户

```shell
sudo su
```

### 退出root用户

```shell
exit
```

## 用户权限

### 添加用户

```shell
sudo adduser username
```

### 删除用户

```shell
sudo deluser username
```

### 修改用户密码

```shell
sudo passwd username
```

### 修改用户组

```shell
sudo usermod -g groupname username
```

### 修改用户家目录

```shell
sudo usermod -d /path/to/home username
```

### 修改用户shell

```shell
sudo usermod -s /bin/bash username
```

### 查看用户信息

```shell
id username
```

### 查看用户组

```shell
groups username
```

### 查看用户家目录

```shell
grep username /etc/passwd
```

## 用户组

### 添加用户组

```shell
sudo addgroup groupname
```

### 删除用户组

```shell
sudo delgroup groupname
```

### 修改用户组

```shell
sudo groupmod -n newgroupname oldgroupname
```

### 查看用户组信息

```shell
grep groupname /etc/group
```

### 查看用户组成员

```shell
grep groupname /etc/group | cut -d: -f4
```

## 文件权限

### 查看文件权限

```shell
ls -l filename
```

### 修改文件所有者

```shell
sudo chown username:groupname filename
```

### 修改文件权限

```shell
sudo chmod 644 filename
```

### 修改文件夹权限

```shell
sudo chmod 755 dirname
```

### 修改文件夹及其子文件夹权限

```shell
sudo chmod -R 755 dirname
```

## 权限代码

- r: 读权限
- w: 写权限
- x: 执行权限
- -: 无权限

| 权限 | 数字 |
| --- | --- |
| rwx | 7 |
| rw- | 6 |
| r-x | 5 |
| r-- | 4 |
| -wx | 3 |
| -w- | 2 |
| --x | 1 |

| 所有者 | 所有者组 | 其他用户 | 权限 |
| --- | --- | --- | --- |
| rwx | r-x | r-x | 755 |
| rw- | r-- | r-- | 644 |
| rwx | --- | --- | 700 |
| rw- | --- | --- | 600 |


## 权限示例

- 文件权限: 644

```shell
-rw-r--r--
```

描述: 所有者可读写, 所有者组可读, 其他用户可读

- 文件夹权限: 755

```shell
drwxr-xr-x
```

描述: 所有者可读写执行, 所有者组可读执行, 其他用户可读执行