## UDP协议

## 特点

- 无连接性: 通信前不需要建立连接, 直接发送数据包, 这样就减少了开销和发送数据之间的延迟
- 不可靠性: 无连接性导致不可靠性, 无法保证数据包的顺序, 丢包不重发, 但是可以保证一旦收到数据包, 数据包的内容是正确的
- 速度快: 不需要建立连接, 无需等待确认
- 支持多播和广播: 一个数据包可以同时发送给多个主机
- 数据包大小受限: 数据包的最大长度为64KB，超过这个限制将会被分割或丢弃。

## 应用场景

- 实时应用: 如音频、视频传输，在线游戏等需要低延迟且不需要可靠性的应用
- IP电话: VoIP（Voice over IP）, 语音通话需要快速实时，即便丢失数据包也不会影响通话
- DNS: DNS使用UDP协议进行域名解析，由于DNS查询通常是非关键性操作，在快速响应方面，UDP比TCP更合适。
- SNMP: 简单网络管理协议（SNMP）通常使用UDP作为传输层协议。SNMP用于监控和管理网络设备，并通过UDP协议发送请求和接收响应。
- 广播和多播: UDP支持广播和多播功能，允许将数据一次性发送给多个接收方。这在视频直播、多媒体流传输等场景中非常有用。

## 报文格式

![1693970471960](image/udp/1693970471960.png "UDP报文格式")

## 伪首部

## 伪首部作用

- 伪首部的作用是增加了对数据报文的校验和的准确性。在计算校验和时，使用伪首部字段将IP层的源IP地址、目标IP地址、协议类型以及TCP或UDP的数据报长度包括进去。这样可以避免因为随机错误而导致校验和计算错误。
- 通过使用伪首部，在接收方进行校验时，可以验证数据报是否在传输过程中被修改或损坏。这有助于保证数据的完整性，并增加了数据的可靠性。同时，伪首部也能够帮助识别数据报文应该被传递给哪个应用程序。

## 检验和

UDP在传输层使用检验和来验证数据的完整性。UDP检验和是一个16位的校验和，它由发送方计算并包含在UDP报文头部中。接收方在接收到UDP报文后，也会计算校验和，并与报文头部中的校验和进行比较。

UDP检验和的计算过程如下：

1. 把UDP报文头部的源端口、目标端口、长度字段（包括UDP头部和数据部分）以及全0的校验和字段看作是一个连续的16位二进制数序列。
2. 将该序列按16位进行分组，求每个分组的16位二进制数的和（忽略溢出进位的情况）。
3. 将上述结果取反（按位取反）得到校验和。

如果发送方计算得到的校验和与报文头部中的校验和相匹配，则认为数据完整无误；否则，存在数据错误或损坏。

UDP检验和的目的是检测数据在传输过程中是否被改变或损坏。它可以帮助接收方发现传输中的错误，并丢弃损坏的数据，从而保证数据的完整性。然而，UDP检验和并不提供数据的可靠传输，因为它只能侦测数据的一部分错误情况，而不能纠正错误。如果需要可靠传输，应该使用TCP协议。

## 为什么UDP的检验和是可选的

UDP的检验和是可选的，主要有以下几个原因：

1. UDP本身是一种无连接的传输协议，它的设计目标是简单、高效，并且对于实时性要求较高的应用。因此，为了避免引入额外的计算和处理开销，UDP的设计者选择将检验和作为可选字段。
2. 在某些情况下，网络环境相对稳定且可靠，并且发送方与接收方之间的通信链路质量较好，数据包损坏的概率较低。在这种情况下，进行校验和计算可能被认为是多余的，只会增加传输开销而不会带来太大的好处。
3. 对于某些特定的应用场景，数据完整性可以由应用层自行保证，因此不需要依赖UDP的检验和。例如，在音频或视频流传输中，即使出现了少量的数据损坏也不会对最终的音视频质量产生明显的影响，因此可以通过其他方式来保证数据的完整性。

虽然UDP的检验和是可选的，但在某些情况下仍然建议使用它。特别是在网络不稳定或可靠性要求较高的应用场景中，启用UDP的检验和可以提供一定的数据完整性保证，帮助检测并丢弃损坏的数据包。

## UDP的检验和为什么不是可靠的

UDP的检验和不是可靠的，主要有以下几个原因：

1. UDP的检验和是一个简单的校验算法，仅仅对数据进行累加运算并取反。这种算法虽然可以检测出一部分错误，但并不能保证100%的准确性。例如，只有当数据包中出现了偶数个错误比特时，才能正确地通过校验和。
2. UDP的检验和是在传输层进行计算的，并没有经过端到端的校验。这意味着，即使发送方在UDP头部中计算了检验和，但在中间网络节点或接收方之间，数据包仍然可能被修改、丢失或重复。这样就无法依靠UDP的检验和来确保数据完整性。
3. UDP本身是一种不可靠的传输协议，它不提供确认机制或重传机制。因此，即使通过检验和发现了错误的数据包，UDP也不会主动进行纠错或重新发送。这使得UDP的检验和在保证数据可靠性方面无法发挥作用。

因此，如果应用程序对数据的可靠性具有较高要求，建议选择使用TCP协议，它提供了可靠的传输、流量控制、拥塞控制等功能。而UDP主要适用于实时性要求较高、对数据可靠性要求较低的应用，例如音视频传输、游戏通信等。

## 如何通过UDP打洞

## UDP打洞的原理

UDP打洞是一种技术手段，用于在NAT(Network Address Translation)环境中建立两个位于不同私有网络的主机之间的直接通信。

当两台主机A和B同时处于位于不同私有网络的NAT后面时，由于NAT的存在，它们无法直接与对方进行通信。UDP打洞利用了NAT的转发规则和端口映射来解决这个问题。

要实现UDP打洞，可以按照以下步骤进行操作：

1. 选择一个公网可访问的服务器作为中转服务器。
2. 主机A向中转服务器发送一个UDP数据包，并将数据包的源地址和端口设置为主机A的公网IP地址和一个随机端口。数据包的目标地址和端口设置为中转服务器的公网IP地址和一个开放的端口。
3. 中转服务器接收到该数据包后，记录下主机A的IP地址和端口信息。
4. 中转服务器将该数据包转发给主机B。在转发过程中，中转服务器不修改数据包的源地址和端口。
5. 主机B在收到数据包后会认为发送地址是中转服务器的地址。
6. 主机B向中转服务器发送一个UDP数据包，并将数据包的源地址和端口设置为主机B的公网IP地址和一个随机端口。数据包的目标地址和端口设置为中转服务器之前记录的主机A的IP地址和端口。
7. 中转服务器接收到该数据包后，将数据包转发给主机A。在转发过程中，中转服务器不修改数据包的源地址和端口。
8. 主机A在收到数据包后，建立了与主机B之间的直接通信连接。
