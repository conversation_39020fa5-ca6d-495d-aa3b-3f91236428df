# 创建网络

docker network create --subnet=**********/16 dev

# 创建容器

docker run -it -d --net dev --ip ********** --name springboot-dev -h springboot-dev -v C:\xuedinge\code:/home springboot-dev

## 复制文件到容器

docker cp C:\xuedinge\code\springboot-dev\springboot-dev-0.0.1-SNAPSHOT.jar springboot-dev:/home

## 导出镜像

docker save -o image.tar image_name:tag

## 导入镜像

docker load -i image.tar

## 执行容器外本地文件

cat /data1/wms/updatepage.sql | docker exec -i wms-postgres-1 psql -U postgres wms

## 执行容器内命令

docker exec wms-postgres-1 psql -U postgres wms -c "truncate table wms_approval_task_remarks"