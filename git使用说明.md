## git配置

git config  user.name "[username]"
git config user.email "[email]"

## 记住密码
git config credential.helper store  

## 撤回上一个commit

git reset --soft HEAD^

## 撤回上一个commit并且删除工作区的改动

git reset --hard HEAD^

## 删除某个文件/文件夹的改动

git checkout [path]

## 取消全部暂存

git reset HEAD

## 取消某个文件/文件夹的暂存

git reset HEAD [path]

## 回滚提交到远程仓库的某个commit

git revert [commit_id]

## 撤回所有改动

git checkout .

## 撤回本地提交

git reset --soft origin/master



