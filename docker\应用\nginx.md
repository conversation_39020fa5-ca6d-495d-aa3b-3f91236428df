docker pull nginx

docker run -it -d --name nginx nginx

docker cp nginx:/etc/nginx/nginx.conf /mnt/c/xuedinge/data/nginx/nginx.conf
docker cp nginx:/etc/nginx/conf.d /mnt/c/xuedinge/data/nginx/conf.d
docker cp nginx:/usr/share/nginx/html /mnt/c/xuedinge/data/nginx/www
docker cp nginx:/var/log/nginx /mnt/c/xuedinge/data/nginx/logs

docker container stop nginx
docker container rm nginx

docker run -it -d --net dev --name nginx -p 80:80 -p 433:433  -v /mnt/c/xuedinge/data/nginx/nginx.conf:/etc/nginx/nginx.conf -v /mnt/c/xuedinge/data/nginx/conf.d:/etc/nginx/conf.d -v /mnt/c/xuedinge/data/nginx/www:/usr/share/nginx/html -v /mnt/c/xuedinge/data/nginx/logs:/var/log/nginx -v /mnt/c/xuedinge/data/nginx/cert:/etc/nginx/cert nginx