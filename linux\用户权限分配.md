## 创建用户
### 创建系统管理员
sudo useradd -m sysadmin
sudo passwd sysadmin

### 创建审计管理员
sudo useradd -m auditadmin
sudo passwd auditadmin

### 创建安全管理员
sudo useradd -m secadmin
sudo passwd secadmin

## 创建对应的用户组
### 创建管理员组
sudo groupadd sysadmins
sudo groupadd auditadmins
sudo groupadd secadmins

### 将用户添加到对应组
sudo usermod -aG sysadmins sysadmin
sudo usermod -aG auditadmins auditadmin
sudo usermod -aG secadmins secadmin

## 配置权限
### 系统管理员权限
#### 允许系统管理员执行系统维护命令
echo "sysadmin ALL=(ALL) /bin/systemctl, /usr/bin/apt, /usr/bin/dpkg" | sudo tee -a /etc/sudoers.d/sysadmin
### 审计管理员权限
#### 允许审计管理员查看日志
echo "auditadmin ALL=(ALL) /usr/bin/journalctl, /bin/cat /var/log/*, /bin/grep" | sudo tee -a /etc/sudoers.d/auditadmin
### 安全管理员权限
#### 允许安全管理员配置防火墙和安全策略
echo "secadmin ALL=(ALL) /usr/sbin/ufw, /usr/bin/passwd, /usr/sbin/usermod" | sudo tee -a /etc/sudoers.d/secadmin

## 验证设置
### 测试系统管理员
su - sysadmin
sudo systemctl status sshd  # 应该成功
sudo journalctl  # 应该失败

### 测试审计管理员
su - auditadmin
sudo journalctl  # 应该成功
sudo systemctl restart sshd  # 应该失败

### 测试安全管理员
su - secadmin
sudo ufw status  # 应该成功
sudo apt update  # 应该失败

## 查看用户列表
cat /etc/passwd