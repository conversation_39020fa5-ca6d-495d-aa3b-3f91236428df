## 清理不用的docker数据
```shell
# 清理所有未被使用的镜像
docker image prune -a
# 清理所有未被使用的容器
docker container prune
# 清理所有未被使用的卷
docker volume prune
# 清理所有未被使用的网络
docker network prune
# 清理所有未被使用的构建缓存
docker builder prune
```

## 清理容器日志
find /var/lib/docker/containers/ -type f -name "*.log" -exec truncate -s 0 {} \;


## docker配置自动清理

编辑/etc/docker/daemon.json文件，添加以下内容：
```json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
```
重启docker服务