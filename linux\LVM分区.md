## 安装LVM
yum install lvm2
## 查看磁盘
1. fdisk -l
2. lsblk
3. df -h
## 进入磁盘分区程序
fdisk /dev/sda
## 创建物理卷
pvcreate /dev/sda1
## 创建虚拟卷(卷组)
vgcreate disk1 /dev/sda1
## 创建逻辑卷
lvcreate -L 3.63T -n data disk1
## 查看逻辑卷
lvdisplay
## 格式化逻辑卷
mkfs.ext4 /dev/disk1/data
## 创建挂载点
mkdir /data
## 加入卷组配置
打开/etc/fstab
添加 /dev/disk1/data /data ext4 defaults 0 0
## 挂载
mount -a
## 卸载挂载
删掉 /etc/fstab的配置
umount /data
## 删除逻辑卷
lvremove /dev/disk1/data
## 删除虚拟卷(卷组)
vgchange -a n disk1
vgremove disk1
## 删除物理卷
pvremove /dev/sda1

## LVM扩容
1. fdisk /dev/sda
2. pvcreate /dev/sda2
3. vgextend disk1 /dev/sda2
4. lvextend -L +3.63T /dev/disk1/data
5. resize2fs /dev/disk1/data
```