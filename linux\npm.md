## NPM包管理

### 查看npm版本
```bash
npm -v
```

### 查看已安装的包
```bash
npm ls
```

### 查看全局已安装的包
```bash
npm ls -g
```

### 安装包
```bash
npm install <package-name>
```

### 安装包到全局
```bash
npm install -g <package-name>
```

### 更新包
```bash
npm update <package-name>
```

### 更新全局包
```bash
npm update -g <package-name>
```

### 卸载包
```bash
npm uninstall <package-name>
```

### 卸载全局包
```bash
npm uninstall -g <package-name>
```