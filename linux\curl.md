# curl使用说明

## 基本使用

```shell
curl http://www.baidu.com
```

## 下载文件

```shell
curl -O http://www.baidu.com/index.html
```

## 保存文件

```shell
curl -o index.html http://www.baidu.com
```

## 上传文件

```shell
curl -F "file=@/home/<USER>" -F "name=filename" http://www.baidu.com/upload
```

## Get请求

```shell
curl http://www.baidu.com?name=filename
```

## Post请求

```shell
curl -X POST -d "name=filename" http://www.baidu.com
```

## 设置请求头

```shell
curl -H "Content-Type: application/json" http://www.baidu.com
```

## 设置超时时间

```shell
curl --connect-timeout 3 http://www.baidu.com
```

## 设置User-Agent

```shell
curl -A "Mozilla/5.0" http://www.baidu.com
```

## 设置Referer

```shell
curl -e "http://www.baidu.com" http://www.baidu.com
```

## 设置Cookie

```shell
curl -b "name=filename" http://www.baidu.com
```

## 设置代理

```shell
curl -x http://agent:port http://www.baidu.com
```

## 设置重定向

```shell
curl -L http://www.baidu.com
```