# 离线安装

## 安装yum-utils工具

```shell
yum install -y yum-utils
```

## 配置docker的yum源

```shell
yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
```

## 下载docker-ce包和所有依赖包
    
```shell
yumdownloader --resolve docker-ce
```
## 将包复制到离线环境

## 安装所有rpm包

```shell
sudo yum localinstall *.rpm
```
## 启动docker服务

```shell
systemctl start docker
```

## 设置开机启动

```shell
systemctl enable docker
```