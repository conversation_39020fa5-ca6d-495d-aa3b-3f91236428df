## 执行sql
docker exec wms-postgres-1 psql -U postgres wms -c "truncate table wms_approval_task_remarks"

## 备份
docker exec wms-postgres-1 pg_dump -U postgres wms > admin-server1.sql

## 还原
cat your_database_backup.sql | docker exec -i your_postgres_container psql -U postgres_user -d your_database

## 停止所有数据库的连接
docker exec admin-server-dev-postgres-1 psql -U postgres postgres -c  "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'demo'";

## 重命名数据库
docker exec admin-server-dev-postgres-1 psql -U postgres postgres -c  "update pg_database set datname='demo' where datname='demo1'";

## 删除数据
docker exec admin-server-dev-postgres-1 psql -U postgres postgres -c  "drop database demo";