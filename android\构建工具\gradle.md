# gradle

## 什么是Gradle

Gradle是一个基于Apache Ant和Apache Maven概念的项目自动化构建工具。它使用一种基于Groovy的特定领域语言（DSL）来声明项目设置，抛弃了基于XML的各种繁琐配置。

## 使用

1. 安装gradle

2. 创建gradle
```shell
    gradle init
```
3. 添加配置
```txt
    build.gradle || settings.gradle
```

4. 执行gradle
```shell
    gradle build
```

在app模块的`build.gradle`文件中添加配置

## 配置

### 声明插件
    
```groovy
plugins {
    id 'java'
}
```

### 配置仓库，依赖库的来源

```groovy
repositories {
    mavenCentral()
}
```

### 配置依赖

```groovy
dependencies {
    implementation 'com.google.guava:guava:30.1-jre'
}
```

### 配置任务

```groovy
task hello {
    doLast {
        println 'Hello world!'
    }
}
```

### 配置属性

```groovy
ext {
    springVersion = '5.3.9'
}
```

### kotlin写法

```kotlin
plugins {
    id("java")
}

repositories {
    mavenCentral()
}

dependencies {
    implementation("com.google.guava:guava:30.1-jre")
}

tasks {
    register("hello") {
        doLast {
            println("Hello world!")
        }
    }
}

val springVersion by extra("5.3.9")
```
