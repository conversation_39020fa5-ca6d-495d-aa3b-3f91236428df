## 查看端口占用
netstat -ano | findstr 9180
## 终止进程
taskkill /F /pid 98108

## 查看系统的保留端口
netsh interface ipv4 show excludedportrange protocol=tcp
## 排除系统的保留端口
net stop winnat
net stop LanmanWorkstation
net stop WlanSvc
netsh int ipv4 add excludedportrange protocol=tcp startport=2379 numberofports=1
net start winnat
net start LanmanWorkstation
net start WlanSvc

## 系统扫描修复
DISM /Online /Cleanup-Image /Scanhealth

DISM /Online /Cleanup-Image /Restorehealth

sfc /scannow