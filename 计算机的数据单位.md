## 计算机的数据单位
1. 位(bit)：计算机中最小的数据单位，只能表示0或1，是二进制的最小单位。
2. 字节(Byte)：计算机中最基本的存储单位，每个字节由8个二进制位组成，可以表示0~255之间的整数。
3. 字(Word)：由若干个字节组成，每个字节由8个二进制位组成，可以表示0~65535之间的整数。
4. 千字节(KB)：1KB=1024B
5. 兆字节(MB)：1MB=1024KB
6. 吉字节(GB)：1GB=1024MB
7. 太字节(TB)：1TB=1024GB
8. 拍字节(PB)：1PB=1024TB
9. 艾字节(EB)：1EB=1024PB
10. 泽字节(ZB)：1ZB=1024EB
11. 尧字节(YB)：1YB=1024ZB

## 为甚么计算机中的数据单位是1024的倍数？
计算机使用的是二进制系统，进行幂次运算只需要移位即可，效率比10进制的乘法运算快很多。所以计算机中的数据单位都是1024的倍数。

## 二进制前缀
基于2的幂的转换方式被称为二进制前缀，是计算机中的数据单位的标准。