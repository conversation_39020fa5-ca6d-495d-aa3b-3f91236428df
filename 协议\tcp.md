## TCP协议

### 特点

- 面向连接: TCP是一种面向连接的协议，意味着在数据传输之前，需要建立一个连接。连接的建立包括三次握手过程，而连接的断开则需要四次挥手过程。
- 可靠性: TCP使用确认和重传机制来确保数据的可靠性传输。接收方会发送确认消息来确认已经成功接收到数据，如果发送方没有收到确认消息，它会自动重传数据。
- 有序性: TCP保证数据按照发送的顺序进行传输，因此接收方能够按照正确的顺序重组数据。
- 流量控制: TCP使用滑动窗口协议来控制发送方的数据发送速度。接收方可以根据自己的处理能力和缓冲区大小来通知发送方适当的发送速度，以防止包丢失和网络拥塞。
- 拥塞控制: TCP通过使用拥塞窗口和慢启动算法来控制网络中的拥塞情况。它会根据网络的负载情况调整发送速度，以避免网络拥塞并提高整体性能。
- 全双工通信: TCP支持全双工通信，这意味着双方可以同时发送和接收数据。
- 可靠的传输: TCP使用校验和、序列号、确认消息等机制来保证数据的可靠传输。如果发生数据丢失或损坏，TCP会自动重传数据。

### 应用场景

- Web浏览器和服务器通信：在Web浏览器和服务器之间进行HTTP通信时，使用TCP协议作为可靠的传输层协议。TCP确保了Web页面和其他资源的可靠传输。
- 电子邮件传输：TCP协议用于SMTP（Simple Mail Transfer Protocol）客户端和服务器之间的电子邮件传输。TCP确保了电子邮件的可靠交付。
- 文件传输: TCP协议被用于FTP（File Transfer Protocol）和SFTP（SSH File Transfer Protocol）等文件传输协议中，以实现文件的可靠传输。
- 远程登录: Telnet和SSH协议都基于TCP，用于与远程系统建立安全的终端会话。
- 数据库访问: 当客户端和服务器之间进行数据库通信时，TCP协议被广泛使用。例如，MySQL、PostgreSQL和Oracle等数据库系统使用TCP协议来提供可靠的数据传输。
- 聊天和即时通讯: TCP协议常用于聊天和即时通讯应用，例如IRC（Internet Relay Chat）和XMPP（Extensible Messaging and Presence Protocol）等。
- 远程过程调用（RPC）: RPC协议允许在网络上不同计算机之间进行程序调用。常见的RPC框架如gRPC和Apache Thrift使用TCP协议来传输调用和响应。

### 报文格式

![1693981129972](image/tcp/1693981129972.png)

### 三次握手四次挥手

#### 三次握手

1. 客户端发送SYN包，进入SYN_SEND状态，等待服务器确认
2. 服务器收到SYN包，发送SYN+ACK包，进入SYN_RECV状态
3. 客户端收到SYN+ACK包，发送ACK包，进入ESTABLISHED状态，服务器收到ACK包，进入ESTABLISHED状态

#### 四次挥手

1. 客户端发送FIN包，进入FIN_WAIT_1状态
2. 服务器收到FIN包，发送ACK包，进入CLOSE_WAIT状态
3. 服务器发送FIN包，进入LAST_ACK状态
4. 客户端收到FIN包，发送ACK包，进入TIME_WAIT状态，服务器收到ACK包，进入CLOSED状态

