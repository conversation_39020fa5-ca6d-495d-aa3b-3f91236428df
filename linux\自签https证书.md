## 自签https证书

### 生成私钥

```shell
openssl genrsa -out server.key 2048
```

### 生成证书请求

```shell
openssl req -new -key server.key -out server.csr
```

### 生成证书

```shell
openssl x509 -req -days 365 -in server.csr -signkey server.key -out server.crt
```

### nginx配置

```nginx
server {
    listen 443 ssl;
    server_name localhost;
    ssl_certificate /path/to/server.crt;
    ssl_certificate_key /path/to/server.key;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers HIGH:!aNULL:!MD5;
    location / {
        root /path/to/root;
    }
}
```