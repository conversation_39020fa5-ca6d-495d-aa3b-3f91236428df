# 通信

## 同一设备内通信

- 管道 (Pipe): 管道是一种最基本的进程间通信机制，它可以在父子进程或者兄弟进程之间传递数据。管道是单向的，分为匿名管道和命名管道。

- 命名管道 (Named Pipe): 命名管道是一种有名的管道，可以通过文件系统路径来访问，使得无关进程可以通过共享一个命名管道来传递数据。

- 信号 (Signal): 进程可以通过发送信号给其他进程来通知其执行某个特定操作。信号可以用来中断进程、处理异常或者与用户交互。

- 共享内存 (Shared Memory): 多个进程可以将相同的物理内存区域映射到各自的虚拟地址空间中，从而实现共享内存数据，进程可以直接读写该内存区域，而不需要进行复制操作。

- 消息队列 (Message Queue): 进程可以通过消息队列来发送和接收消息。消息队列可以按照先进先出的原则进行消息传递，多个进程可以同时写入和读取消息。

- 信号量 (Semaphore): 信号量是一种计数器，多个进程可以使用它来实现对共享资源的访问控制，通过对信号量进行P操作和V操作来实现进程间的同步。

- 套接字 (Socket): 套接字是一种网络通信接口，它可以在不同设备或者同一设备上的不同进程之间进行通信。套接字可以实现进程间的网络通信。

## 不同设备通信

通讯协议