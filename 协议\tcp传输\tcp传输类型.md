## 常用类型

- BYTE 字节，最小单位，程序中一般用uint8表示
- WORD 字，2个字节，程序中一般用uint16表示
- DWORD 双字，4个字节，程序中一般用uint32表示
- BYTE[n] 字节数组，n个字节
- BCD[n] 8421码，n个字节
- STRING GBK编码的字符串，最后一个字节为0

## 什么是高位字节，低位字节

- 在计算机系统中, 由于数据的存储方式或传输方式不同，有的处理器是从低地址开始存放字节的，有的处理器是从高地址开始存放字节的，内存中对于字节处理的顺序会不一样，这就涉及到高位字节和低位字节的问题。
- 高位字节：在内存中地址高的字节，如0x1234，0x12为高位字节
- 低位字节：在内存中地址低的字节，如0x1234，0x34为低位字节


## 大端小端

- 大端：高位字节在前，低位字节在后，如0x1234，0x12为高位字节，0x34为低位字节，程序解析时，不需要做任何处理
- 小端：低位字节在前，高位字节在后， 如0x1234，0x34为低位字节，0x12为高位字节，程序解析时，需要将低位字节和高位字节交换位置，才能得到正确的数值

