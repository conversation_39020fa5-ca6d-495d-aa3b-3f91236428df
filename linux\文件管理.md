# 文件管理

## 创建文件

```shell
touch file.txt
```

## 删除文件

```shell
rm file.txt
```

## 查看文件

```shell
cat file.txt
```

## 编辑文件

```shell
vim file.txt
```

## 查找文件

```shell
find / -name file.txt
```

## 查看文件权限

```shell
ls -l file.txt
```

## 修改文件权限

```shell
chmod 777 file.txt
```
## 查看文件大小

```shell
du -h file.txt
```

## 替换文件内容

```shell
sed -i 's/old/new/g' file.txt
```

## 复制文件

```shell
cp file.txt file2.txt
```

## 移动文件

```shell
mv file.txt /path/to/file.txt
```

## 创建软链接

```shell
ln -s file.txt file_link.txt
```

## 复制文件内容
    
```shell
cat file.txt > file2.txt
```

## 创建文件夹
    
```shell
mkdir dir
```

## 删除文件夹

```shell
rm -rf dir
```

## 查看文件夹

```shell
ls dir
```

## 复制文件夹

```shell
cp -r dir dir2
```

## 移动文件夹

```shell
mv dir /path/to/dir
```

## 查找文件夹

```shell
find / -name dir
```

## 查看文件夹大小

```shell
du -sh dir
```

## 查看文件夹权限

```shell
ls -l dir
```

## 修改文件夹权限

```shell
chmod 777 dir
```

## 修改文件夹及其子文件夹权限

```shell
chmod -R 777 dir
```

## 创建软链接

```shell
ln -s dir dir_link
```

## 查看文件夹内容

```shell
tree dir
```

## 查看文件夹内容（包括隐藏文件）

```shell
ls -a dir
```
