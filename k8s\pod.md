## pod

pod是k8s里最小的可部署单元，一个pod里可以包含一个或多个容器，这些容器共享网络和存储资源。

### pod的生命周期

pod的生命周期分为以下几个阶段：

- Pending（等待）：当一个Pod被创建后，它进入Pending状态。在此阶段，Kubernetes调度器会为该Pod选择合适的节点，并将Pod放置在该节点上。同时，也会进行一些预启动的准备工作，例如创建网络和存储资源。
- Running（运行中）：一旦Pod成功地被调度到一个节点上，它进入Running状态。在此状态下，容器会被启动，并开始运行应用程序。Kubernetes会监控Pod的状态并确保其正常运行。
- Succeeded（成功）：如果Pod中的所有容器都成功地完成了它们的任务并退出了，Pod将进入Succeeded状态。这通常是批处理任务或一次性作业完成后的状态。
- Failed（失败）：如果Pod中的任何一个容器以非零状态退出，或者容器在启动过程中遇到错误，Pod将进入Failed状态。这表明Pod内部发生了故障或错误。
- Unknown（未知）：如果Kubernetes无法获取Pod的状态信息，则Pod将进入Unknown状态。这可能是由于与Pod相关的组件出现故障或失去连接造成的。

### pod配置

pod的配置示例：

```yaml

apiVersion: v1
kind: Pod
metadata:
  name: my-pod
spec:
  containers:
    - name: my-container
      image: image_name  

```

#### 配置说明

- apiVersion：指定使用的Kubernetes API版本，通常为v1。
- kind：指定对象类型为Pod。
- metadata：包含与Pod相关的元数据，例如名称、标签和注释。
  - name：Pod的名称。
  - labels：Pod的标签。
  - annotations：Pod的注释。
- spec：定义了Pod的规范，包括其中运行的容器、存储卷等。
  - containers：定义Pod中运行的容器。可以为Pod定义一个或多个容器，每个容器都具有自己的名称、镜像、端口、环境变量等设置。
    - name：容器的名称。
    - image：容器所使用的镜像。
    - ports：容器需要暴露的端口。
    - env：容器的环境变量。
    - volumeMounts：容器挂载的存储卷。
  - volumes：定义Pod所需的持久化存储卷。可以将存储卷挂载到Pod中的容器，以便在容器之间共享数据。
    - name：存储卷的名称。
    - emptyDir：空目录卷，用于存储临时数据。
    - hostPath：主机路径卷，用于将主机上的目录挂载到Pod中。
    - configMap：配置映射卷，用于将配置映射挂载到Pod中。
    - secret：密钥卷，用于将密钥挂载到Pod中。
  - nodeName：定义Pod所在的节点名称。
  - restartPolicy：定义Pod中容器的重启策略。
  - initContainers：定义在Pod中运行的初始化容器。初始化容器在主容器启动之前运行，并可以用于执行一些初始化任务，例如数据库初始化等。
  - imagePullSecrets：定义Pod中容器拉取镜像所需的密钥。
  - nodeSelector：定义Pod所需的节点标签。
  - affinity：定义Pod与其他Pod的亲和关系。
  - tolerations：定义Pod的容错策略。
  - hostNetwork：定义Pod是否使用主机网络。
  - hostPID：定义Pod是否使用主机PID命名空间。
  - hostIPC：定义Pod是否使用主机IPC命名空间。
  - securityContext：定义Pod的安全上下文。
  - dnsPolicy：定义Pod的DNS策略。
  - serviceAccountName：定义Pod所使用的服务账户。
  - schedulerName：定义Pod所使用的调度器。
  - priorityClassName：定义Pod的优先级。
  - priority：定义Pod的优先级。
  - terminationGracePeriodSeconds：定义Pod的优雅终止时间。
  - activeDeadlineSeconds：定义Pod的活动截止时间。
  - automountServiceAccountToken：定义是否自动挂载服务账户的Token。

### pod的命令

```bash

# 创建pod
kubectl create -f pod.yaml

# 查看pod
kubectl get pod

# 查看pod详细信息
kubectl describe pod my-pod

# 删除pod
kubectl delete pod my-pod

```
