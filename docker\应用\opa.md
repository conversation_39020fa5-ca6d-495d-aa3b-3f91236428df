mkdir -p /mnt/c/xuedinge/data/opa
touch /mnt/c/xuedinge/data/opa/config.yaml
docker run -it -d --name opa --net dev -v /mnt/c/xuedinge/data/opa:/config -p 8181:8181 openpolicyagent/opa run --server --addr :8181 --config-file /config/config.yaml

## config 格式
services:
  - name: api
    url: http://api:8080

bundles:
  api:
    service: api
    resource: bundle/bundle.tar.gz
    persist: false
    polling:
      min_delay_seconds: 10
      max_delay_seconds: 20

decision_logs:
  service: api
  reporting:
    min_delay_seconds: 5
    max_delay_seconds: 10