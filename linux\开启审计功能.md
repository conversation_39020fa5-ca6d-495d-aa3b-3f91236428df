## 开启系统内核审计功能
vi /etc/default/grub
找到audit=0 修改为 audit=1
## 更新grub

grub2-mkconfig -o /boot/grub2/grub.cfg

## 重启系统
reboot

## 开启审计功能

systemctl enable auditd
systemctl start auditd

## 确认审计功能已经开启
systemctl status auditd

## 配置审计规则
sudo vi /etc/audit/rules.d/audit.rules

添加以下内容：
```
# 记录用户登录登出
-w /var/log/wtmp -p wa -k logins
-w /var/log/btmp -p wa -k logins

# 记录用户、组的修改
-w /etc/group -p wa -k identity
-w /etc/passwd -p wa -k identity
-w /etc/shadow -p wa -k identity

# 记录系统命令执行
-w /bin/su -p x -k privileged
-w /usr/bin/sudo -p x -k privileged
```

## 重启审计功能
systemctl restart auditd

## 查看审计日志
sudo ausearch -ts today

## 创建审计管理员
### 创建审计管理员账户
sudo useradd -m auditadmin

### 设置密码
sudo passwd auditadmin

### 创建审计日志管理组
sudo groupadd auditgroup

### 将审计管理员加入该组
sudo usermod -aG auditgroup auditadmin

## 设置审计日志文件权限
### 调整日志目录权限
sudo chown -R root:auditgroup /var/log
sudo chmod -R 750 /var/log

### 确保只有审计管理员可以管理日志
sudo find /var/log -type f -exec chmod 640 {} \;


## 配置日志保留策略（保存6个月）
sudo vi /etc/logrotate.conf
添加以下内容：
```
# 每周轮转日志
weekly

# 保留26周（约6个月）的日志
rotate 26

# 压缩旧日志
compress

# 特别配置登录记录
/var/log/wtmp {
    monthly
    create 0664 root utmp
    minsize 1M
    rotate 6
}
```
## 设置日志备份策略
### 创建备份脚本
sudo vi /usr/local/bin/backup_logs.sh
添加以下内容：
```
#!/bin/bash
# 日志备份脚本

# 获取当前日期
DATE=$(date +%Y%m%d)

# 创建备份目录
BACKUP_DIR=/mnt/backup/logs/$DATE
mkdir -p $BACKUP_DIR

# 打包备份日志
tar -czf $BACKUP_DIR/system_logs_$DATE.tar.gz /var/log

# 设置权限
chmod 640 $BACKUP_DIR/system_logs_$DATE.tar.gz
chown root:auditgroup $BACKUP_DIR/system_logs_$DATE.tar.gz
```
### 设置权限并添加定时任务：
#### 设置脚本权限
sudo chmod 700 /usr/local/bin/backup_logs.sh

#### 编辑定时任务
sudo crontab -e
添加以下内容：
```
# 每天凌晨2点备份日志
0 2 * * * /usr/local/bin/backup_logs.sh
```

##  验证效果
### 检查权限设置
ls -la /var/log

### 测试日志轮转
sudo logrotate -f /etc/logrotate.conf

### 检查备份是否成功
sudo /usr/local/bin/backup_logs.sh
ls -la /mnt/backup/logs/