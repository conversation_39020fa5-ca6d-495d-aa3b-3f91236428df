## 修复wsl硬盘问题: Read-only file system报错

1. 查看挂载的硬盘

``` bash
mount | grep ext4
```

2. 挂载硬盘

``` bash
sudo e2fsck /dev/sdc -y
```

3. 在powershell中关闭wsl，

``` bash
wsl --shutdown
```

4. 重启之后即可

## wsl服务对外访问

1. 设置本地端口转发到wsl

``` bash
netsh interface portproxy add v4tov4 listenport=80 listenaddress=0.0.0.0  connectport=80 connectaddress=localhost
```

1. 查看端口转发信息
    
``` bash
netsh interface portproxy show all
```

3. 防火墙开放端口

``` bash
netsh advfirewall firewall add rule name="Open Port 80" dir=in action=allow protocol=TCP localport=80
```

## 删除端口转发

``` bash
netsh interface portproxy delete v4tov4 listenport=80 listenaddress=0.0.0.0
```

## 压缩磁盘

``` bash
diskpart
select vdisk file="C:\Users\<USER>\AppData\Local\Packages\CanonicalGroupLimited.Ubuntu22.04LTS_79rhkp1fndgsc\LocalState"
compact vdisk
exit
```

## wsl配置

1. 创建.wslconfig文件 C:\Users\<USER>\.wslconfig
2. 添加以下内容

``` bash
[wsl2]
# 设置wsl2的内存和cpu
memory=4GB
processors=2

[expermiental]
# 设置wsl2的内存回收策略 gradual 逐渐释放 dropcache 立即释放 disabled 禁用
autoMemoryReclaim=dropcache
# 设置wsl2的虚拟硬盘为稀疏模式，可以自动回收空间
sparseVhd=true
```


