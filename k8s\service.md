## service

service 是 k8s 中的一个重要概念，它是一个抽象层，用来将一组具有相同功能的 pod 组合成一个服务单元，对外提供服务。

### service的类型

service 有以下几种类型：

- ClusterIP：默认类型，为集群内部提供访问服务。只能在集群内部访问，外部无法访问。
- NodePort：在每个节点上随机选择一个端口，通过该端口可以访问Service。可以在集群内部访问，也可以在集群外部访问。
- LoadBalancer：创建一个公共负载均衡器，用于将流量转发到Service。可以在集群内部访问，也可以在集群外部访问。
- ExternalName：在集群外部映射Service到一个外部地址。只能在集群内部访问，外部无法访问。可以将请求转发到集群外部的服务。

### service配置

service 配置示例：

```yaml

apiVersion: v1
kind: Service
metadata:
  name: nginx
spec:
    type: NodePort
    selector:
        app: nginx
    ports:
        - name: http
        port: 80
        targetPort: 80
        nodePort: 30080
        
```

