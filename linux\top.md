# top 使用说明

## 命令

- `top`：显示系统中各个进程的资源占用情况
- `q`：退出 top
- `h`：显示帮助信息
- `k`：kill 进程
- `M`：按内存使用量排序
- `P`：按 CPU 使用量排序
- `T`：按时间排序
- `1`：显示每个 CPU 的使用情况
- `f`：显示字段

## 显示内容描述

cpu 行：

- us：用户空间占用 CPU 百分比
- sy：内核空间占用 CPU 百分比
- ni：用户进程空间内改变过优先级的进程占用 CPU 百分比
- id：空闲 CPU 百分比
- wa：等待输入输出的 CPU 时间百分比
- hi：硬中断（Hardware IRQ）占用 CPU 时间百分比
- si：软中断（Software Interrupts）占用 CPU 时间百分比
- st：虚拟机占用 CPU 时间百分比
- %Cpu(s)：总的 CPU 使用率 超出 100% 时，表示有多个 CPU
- %us：用户空间占用 CPU 百分比
- %sy：内核空间占用 CPU 百分比
- %ni：用户进程空间内改变过优先级的进程占用 CPU 百分比
- %id：空闲 CPU 百分比
- %wa：等待输入输出的 CPU 时间百分比
- %hi：硬中断（Hardware IRQ）占用 CPU 时间百分比
- %si：软中断（Software Interrupts）占用 CPU 时间百分比
- %st：虚拟机占用 CPU 时间百分比

内存行：

- total：总内存
- used：已使用内存
- free：空闲内存
- buffers：缓冲区
- cached：缓存