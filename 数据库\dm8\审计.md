## 开启审计功能

SP_SET_ENABLE_AUDIT (1);

## 打开全部语句审计

SP_AUDIT_STMT('ALL', 'NULL', 'ALL');

## 关闭全部审计

## SP_NOAUDIT_STMT('ALL', 'NULL', 'ALL');

## 登录登出审计

SP_AUDIT_STMT('CONNECT', 'NULL', 'ALL');

## 取消登录登出审计

SP_NOAUDIT_STMT('CONNECT', 'NULL', 'ALL');

## 打开某模式的表审计

SP_AUDIT_STMT('SELECT TABLE', 'DMHR', 'ALL');

## 取消某模式的表审计

SP_NOAUDIT_STMT('SELECT TABLE', 'DMHR', 'ALL');

## 查看审计记录

select * from V$AUDITRECORDS;

## 查看结果
SELECT * FROM V$DM_INI WHERE PARA_NAME='ENABLE_AUDIT';

## 修改审计日志大小 sysdba用户执行
SP_SET_PARA_VALUE (1,'AUDIT_MAX_FILE_SIZE',200)

## 删除审计记录
SP_DROP_AUDIT_FILE('2019-12-6 16:30:00',0);

## 设置审计记录备份策略

### 创建备份脚本 (保存为 /data/backup_audit.sh)
``` shell
#!/bin/bash
export DM_HOME=/data/dmdba/dmdbms
export PATH=$PATH:$DM_HOME/bin
export LD_LIBRARY_PATH=$DM_HOME/bin:$LD_LIBRARY_PATH
export LANG=zh_CN.UTF-8

date=$(date +%Y%m%d%H%M)
bakuser=SYSAUDITOR
bakpwd=sj_axQjw001
dmserver=172.16.214.202:5236
bakdir=/data/dm8/backup
bakfile="audit_${date}.dmp"
logfile="audit__${date}.log"

# 导出审计记录到独立文件
disql "${bakuser}/${bakpwd}@${dmserver}" <<EOF
select * from V\$AUDITRECORDS;
EOF > "${bakdir}/${bakfile}"
```
disql "${bakuser}/${bakpwd}@${dmserver}" -E "select * from V\$AUDITRECORDS;" > "${bakdir}/${logfile}"
## 设置定时任务自动执行备份
### 编辑定时任务
crontab -e

### 添加以下内容（每周日凌晨2点执行备份）
0 2 * * 0 /data/backup_audit.sh