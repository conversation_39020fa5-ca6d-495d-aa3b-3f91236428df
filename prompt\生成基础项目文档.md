请分析当前项目的代码结构，生成包含以下内容的开发文档：
1. 功能概述（用生活化比喻描述核心功能，例如："这套代码就像智能咖啡机，通过用户偏好数据自动调配饮品配方"）
2. 核心模块说明（用<模块名>_功能_技术栈格式说明，示例："user_auth模块 ▸ 采用JWT实现用户认证 ▸ 包含登录校验、令牌刷新等功能"）
3. 代码地图（按目录结构分级说明，示例：）
   📁 src
   ├─ 🐍 main.py → 程序入口，初始化配置并启动服务
   ├─ 📁 utils → 工具库集合
   │  └─ data_cleaner.py → 数据预处理工具，含缺失值处理算法
4. 运行指南（检查requirements.txt是否存在依赖列表，若存在则自动生成安装命令）
5. 扩展建议（指出可优化/扩展的功能点）
6. 将文档保存为markdown格式，文件名称为：开发文档.md
请用自然语言解释技术实现，避免专业术语堆砌。涉及路径时使用→符号连接，关键文件用🐍等符号标注