# vim使用指南

## 1. 基本操作

### 1.1 移动光标

- `h` 左移
- `j` 下移
- `k` 上移
- `l` 右移
- `w` 移动到下一个单词的开头
- `W` 移动到下一个单词的开头（忽略标点符号）
- `b` 移动到上一个单词的开头
- `e` 移动到当前单词的末尾
- `0` 移动到行首
- `$` 移动到行尾
- `gg` 移动到文件开头
- `G` 移动到文件末尾
- `H` 移动到屏幕顶端
- `M` 移动到屏幕中间
- `:n` 移动到第n行
- `nG` 移动到第n行
- `n` 向下移动n行
- `N` 向上移动n行
- `Ctrl + f` 向下翻页
- `Ctrl + b` 向上翻页
- `Ctrl + d` 向下翻半页
- `Ctrl + u` 向上翻半页
- `Ctrl + e` 向下滚动一行
- `Ctrl + y` 向上滚动一行
- `zt` 将当前行置于屏幕顶端
- `zz` 将当前行置于屏幕中间
- `zb` 将当前行置于屏幕底端
- `H` 将当前行置于屏幕顶端
- `M` 将当前行置于屏幕中间
- `L` 将当前行置于屏幕底端
- `fx` 移动到下一个x字符处
- `Fx` 移动到上一个x字符处
- `tx` 移动到下一个x字符前
- `Tx` 移动到上一个x字符前
- `;` 重复上一个f, F, t, T命令
- `,` 反向重复上一个f, F, t, T命令
- `%` 移动到匹配的括号处
- `*` 向下查找光标所在的单词
- `#` 向上查找光标所在的单词
- `n` 重复上一个搜索
- `N` 反向重复上一个搜索
- `f{char}` 向后查找字符
- `F{char}` 向前查找字符
- `t{char}` 向后查找字符前一个字符
- `T{char}` 向前查找字符后一个字符

### 1.2 插入文本

- `i` 在光标前插入
- `I` 在行首插入
- `a` 在光标后插入
- `A` 在行尾插入
- `o` 在当前行后插入新行
- `O` 在当前行前插入新行
- `r` 替换当前字符
- `R` 替换当前字符及其后的字符
- `s` 替换当前字符
- `S` 替换当前行
- `cc` 替换当前行
- `C` 替换当前字符到行尾
- `cw` 替换当前单词
- `c$` 替换当前字符到行尾
- `x` 删除当前字符
- `X` 删除前一个字符
- `dw` 删除当前单词
- `dd` 删除当前行
- `D` 删除当前字符到行尾
- `d$` 删除当前字符到行尾
- `d0` 删除当前字符到行首
- `d^` 删除当前字符到行首
- `dgg` 删除当前行到文件开头
- `dG` 删除当前行到文件末尾
- `d{n}j` 删除当前行及下面n行
- `d{n}k` 删除当前行及上面n行
- `u` 撤销
- `Ctrl + r` 重做
- `.` 重复上一个命令
- `p` 粘贴
- `P` 粘贴到当前行前
- `yy` 复制当前行
- `y$` 复制当前字符到行尾
- `y0` 复制当前字符到行首
- `y^` 复制当前字符到行首
- `ygg` 复制当前行到文件开头
- `yG` 复制当前行到文件末尾
- `y{n}j` 复制当前行及下面n行
- `y{n}k` 复制当前行及上面n行
- `Y` 复制当前行
- `J` 合并当前行和下一行
- `>G` 缩进到文件末尾
- `>gg` 缩进到文件开头
- `>nG` 缩进到第n行
- `>n` 向右缩进n次
- `<G` 反缩进到文件末尾
- `<gg` 反缩进到文件开头
- `<nG` 反缩进到第n行
- `<n` 向左缩进n次
- `=` 自动缩进
- `==` 自动缩进当前行
- `=G` 自动缩进到文件末尾
- `=gg` 自动缩进到文件开头
- `=nG` 自动缩进到第n行
- `=n` 自动缩进n次
- `~` 切换大小写
- `g~w` 切换当前单词大小写
- `g~$` 切换当前字符到行尾大小写
- `g~0` 切换当前字符到行首大小写
- `g~^` 切换当前字符到行首大小写
- `g~gg` 切换当前行到文件开头大小写
- `g~G` 切换当前行到文件末尾大小写
- `g~{n}j` 切换当前行及下面n行大小写
- `g~{n}k` 切换当前行及上面n行大小写
- `g~w` 切换当前单词大小写
