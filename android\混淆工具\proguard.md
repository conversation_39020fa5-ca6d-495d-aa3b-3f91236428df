# proguard

## 什么是ProGuard

ProGuard是一个Java类文件的优化、混淆、压缩工具。它可以移除无用的类、字段、方法和属性。同时可以混淆类、字段和方法的名称，使得代码难以阅读。ProGuard还可以压缩Java字节码文件，使得最终的APK包更小。

## 使用

### 配置

在`app`模块的`build.gradle`文件中添加如下配置：

```groovy
android {
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

### 混淆规则

在`app`模块的`proguard-rules.pro`文件中添加混淆规则：

-keep class com.example.SomeClass // 保持某个类不被混淆
-keep class * extends com.example.SomeClass // 保持某个类的子类不被混淆
-keep public class * implements com.example.SomeInterface // 保持某个接口的实现类不被混淆
-keepnames class * implements com.example.SomeInterface // 保持某个接口的实现类的名称不被混淆
-keep class * implements com.example.SomeInterface{ public <init>(); } // 保持某个接口的实现类的构造方法不被混淆
-dontwarn com.example.SomeClass // 忽略某个类的警告
-printmapping mapping.txt // 输出混淆映射文件