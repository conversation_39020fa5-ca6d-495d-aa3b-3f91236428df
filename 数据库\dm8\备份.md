## 导出数据

```shell
./dexp USERID=SYSDBA/sj_axQjw001@localhost:5236 FILE=/data/dm8/backup/backup.dmp DIRECTORY=/data/dm8/dmdba/dmdbms/bin LOG=/data/dm8/backup/backup.log SCHEMAS=GORMDEMO
```

## 导入数据

```shell
./dimp USERID=SYSDBA/sj_axQjw001@localhost:5236 FILE=/data/dm8/backup/backup.dmp DIRECTORY=/data/dm8/dmdba/dmdbms/bin LOG=/data/dm8/backup/backup.log SCHEMAS=GORMDEMO

# 表存在替换的导入
./dimp USERID=SYSDBA/sj_axQjw001@localhost:5236 FILE=/data/imp_exp_20250213115520.dmp DIRECTORY=/data/dm8/dmdba/dmdbms/bin LOG=/data/imp_exp_20250213115520.log TABLE_EXISTS_ACTION=REPLACE SCHEMAS=GORMDEMO

# schema不一样的导入
./dimp USERID=SYSDBA/sj_axQjw001@localhost:5236 FILE=/data/GORMDEMO_202503200100.dmp DIRECTORY=/data/dm8/dmdba/dmdbms/bin LOG=/data/backup.log REMAP_SCHEMA=GORMDEMO:GORMDEMOAUDIT
```

## 定时备份

```shell
export DM_HOME=/data/dmdba/dmdbms
export PATH=$PATH:$DM_HOME/bin
export LD_LIBRARY_PATH=$DM_HOME/bin:$LD_LIBRARY_PATH
export LANG=zh_CN.UTF-8

date=$(date +%Y%m%d%H%M)
bakdir=/data/dm8/backup
days=7
dmserver=**************:5236
bakuser=SYSDBA
bakpwd=sj_axQjw001
schema=GORMDEMO
bakfile="${schema}_${date}.dmp"
logfile="${schema}_${date}.log"
dmdatabak="${schema}_${date}.tar.gz"

find "${bakdir}" -type f -name "*.tar.gz" -mtime +$days -exec rm -f {} \;

dexp "${bakuser}/${bakpwd}@${dmserver}" FILE="${bakdir}/${bakfile}" DIRECTORY="${bakdir}" LOG="${bakdir}/${logfile}" SCHEMAS="${schema}"
dexp_exit_status=$?
if [$dexp_exit_status -ne 0]; then
    echo "dexp failed"
    exit 1
fi

tar -zcvf "${bakdir}/${dmdatabak}" "${bakdir}/${bakfile}" "${bakdir}/${logfile}"
tar_exit_status=$?
if [$tar_exit_status -ne 0]; then
    echo "tar failed"
    exit 1
fi

find $bakdir -type f -name "*.log" -mtime +$days -exec rm -f {} \;
find $bakdir -type f -name "*.dmp" -exec rm  {} \;
```

### 每晚1点执行备份
    
```shell
crontab -e
```
    
```shell
0 1 * * * /data/dm8/dmdba/dmdbms/scripts/backup.sh
```