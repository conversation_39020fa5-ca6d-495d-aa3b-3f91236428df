# 运维管理

## mysql登录

```bash
mysql -u 用户名 -p
```

## 远程mysql登录

```bash
mysql -h 服务器地址 -P 端口 -u 用户名 -p
```

## 查看mysql数据库大小

```sql
SELECT table_schema AS 'Database', 
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' 
    FROM information_schema.TABLES 
    GROUP BY table_schema;
```

## 查看mysql数据库列表
    
```sql
SHOW DATABASES;
```

## 查看mysql表列表
    
```sql
SHOW TABLES;
```

## 查看mysql数据存放地址

```sql
SHOW VARIABLES LIKE 'datadir';
```

## 查看mysql内存占用

```sql
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
```

## 备份mysql数据库

```bash
mysqldump -u 用户名 -p 数据库名 > 备份文件名
```

```bash
mysqldump -h 服务器地址 -P 端口 -u 用户名 -p 数据库名 > 备份文件名
```

## 查看文件夹大小

```bash
du -sh 文件夹名
```

## 查看磁盘空间

```bash
df -h
```

## 查看内存

```bash
free -h
```

## 查看cpu

```bash
top
```

## 查看进程

```bash
## 详细版
ps -aux | head -n 1 && ps -aux | grep 进程名
## 精简版
ps -a | head -n 1 && ps -a | grep 进程名
```

## 实时查看进程

```bash
top  -p $(pgrep -d',' -f 进程名)
```

## mongodb登录

```bash
mongo -u 用户名 -p 密码 --authenticationDatabase 授权数据库
```

## mongodb查看磁盘占用

```mongo
db.stats(1024 * 1024)
```

## mongodb查看内存占用

```mongo
db.serverStatus().mem
```

## mongodb查看数据存储路径

```mongo
db.adminCommand({ getParameter: 1, storage: 1 })
```

## rabbitmq查看状态

```bash
rabbitmqctl status
```

## rabbitmq查看队列

```bash
rabbitmqctl list_queues
```

## rabbitmq查看交换机

```bash
rabbitmqctl list_exchanges
```

## rabbitmq查看绑定

```bash
rabbitmqctl list_bindings
```

## rabbitmq查看内存占用

```bash
rabbitmqctl status | grep -A 3 memory
```

## rabbitmq查看磁盘占用

```bash
rabbitmqctl status | grep -A 3 disk
```

## rabbitmq查看安装路径

```bash
rabbitmqctl status | grep -A 3 db_dir
```

## redis登录

```bash
redis-cli -h 服务器地址 -p 端口 -a 密码
```

## redis查看内存占用

```bash
redis-cli -h 服务器地址 -p 端口 -a 密码 info memory
```

## redis查看磁盘占用

```bash
redis-cli -h 服务器地址 -p 端口 -a 密码 info persistence
```

## redis查看数据存放路径

```bash
redis-cli -h 服务器地址 -p 端口 -a 密码 config get dir
```
