fallocate -l 700G large_file_700GB

stress-ng --vm 2 --vm-bytes 1G --timeout 3600s

## 占用资源脚本
```shell
#!/bin/bash

# 资源占用脚本
memsize=5G
filesize=800G

echo "$(date) 开始资源占用"

# 占用内存（保留缓冲）
stress-ng --vm 1 --vm-bytes $memsize --vm-keep  &

# 创建10G稀疏文件（快速分配）
fallocate -l $filesize /data/hogfile

echo "$(date) 资源占用已启动"
```

## 停止资源占用脚本
```shell
#!/bin/bash
# 资源释放脚本

echo "$(date) 开始释放资源"

# 精确匹配stress-ng进程（避免误杀）
pkill -f "stress-ng.*"

# 删除占位文件（带验证）
if [[ -f /data/hogfile ]]; then
    rm -vf /data/hogfile
else
    echo "未找到hogfile"
fi

echo "$(date) 资源释放完成"
```