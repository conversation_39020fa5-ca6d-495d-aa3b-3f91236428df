## 安装同步时间服务器

yum install ntpdate
/usr/sbin/ntpdate cn.pool.ntp.org
hwclock --systohc

## 下载mongodb

wget https://fastdl.mongodb.org/linux/mongodb-linux-x86_64-rhel70-5.0.9.tgz
tar zvxf mongodb-linux-x86_64-rhel70-5.0.9.tgz
mv mongodb-linux-x86_64-rhel70-5.0.9 mongodb-5.0.9
mv mongodb-5.0.9 /usr/

## 创建数据目录

### 创建mongos目录

mkdir -p /data/mongodb/mongos/data /data/mongodb/mongos/log /data/mongodb/mongos/run

### 创建config server目录

mkdir -p /data/mongodb/config/data /data/mongodb/config/log /data/mongodb/config/run

### 创建shard1目录

mkdir -p /data/mongodb/shard1/data /data/mongodb/shard1/log /data/mongodb/shard1/run

### 创建shard2目录

mkdir -p /data/mongodb/shard2/data /data/mongodb/shard2/log /data/mongodb/shard2/run

### 创建shard3目录

mkdir -p /data/mongodb/shard3/data /data/mongodb/shard3/log /data/mongodb/shard3/run

## mongos sever(守护进程服务)

touch /data/mongodb/mongos/mongod.conf
添加配置:
systemLog:
    destination: file
    logAppend: true
    path: /data/mongodb/mongos/log/mongod.log
processManagement:
    fork: true
    pidFilePath: /data/mongodb/mongos/run/mongod.pid
net:
    port: 27200
sharding:
    configDB: config/*************:27100,*************:27100,*************:27100

touch /usr/lib/systemd/system/mongos.service
添加配置:
[Unit]
Description=MongoDB Database mongos Service
Wants=network.target
After=network.target

[Service]
Type=forking
PIDFile=/data/mongodb/mongos/run/mongod.pid
ExecStart=/usr/mongodb-5.0.9/bin/mongos -f /data/mongodb/mongos/mongod.conf
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
StandardOutput=syslog
StandardError=syslog

[Install]
WantedBy=multi-user.target

systemctl daemon-reload
systemctl enable mongos

## 配置config sever(路由服务器)

touch /data/mongodb/config/mongod.conf
添加配置:
systemLog:
    destination: file
    logAppend: true
    path: /data/mongodb/config/log/mongod.log
storage:
    dbPath: /data/mongodb/config/data
    journal:
        enabled: true
processManagement:
    fork: true
    pidFilePath: /data/mongodb/config/run/mongod.pid
net:
    port: 27100
replication:
    replSetName: config
sharding:
    clusterRole: configsvr

touch /usr/lib/systemd/system/mongod-config.service
添加配置:
[Unit]
Description=MongoDB Database config Service
Wants=network.target
After=network.target

[Service]
Type=forking
PIDFile=/data/mongodb/config/run/mongod.pid
ExecStart=/usr/mongodb-5.0.9/bin/mongod -f /data/mongodb/config/mongod.conf
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
StandardOutput=syslog
StandardError=syslog

[Install]
WantedBy=multi-user.target

systemctl daemon-reload
systemctl enable mongod-config

## 配置分片

### 配置shard1

touch /data/mongodb/shard1/mongod.conf
添加配置:
systemLog:
    destination: file
    logAppend: true
    path: /data/mongodb/shard1/log/mongod.log
storage:
    dbPath: /data/mongodb/shard1/data
    journal:
        enabled: true
processManagement:
    fork: true
    pidFilePath: /data/mongodb/shard1/run/mongod.pid
net:
    port: 27001
replication:
    replSetName: shard1
sharding:
    clusterRole: shardsvr

touch /usr/lib/systemd/system/mongod-shard1.service
添加配置:
[Unit]
Description=MongoDB Database shard1 Service
Wants=network.target
After=network.target

[Service]
Type=forking
PIDFile=/data/mongodb/shard1/run/mongod.pid
ExecStart=/usr/mongodb-5.0.9/bin/mongod -f /data/mongodb/shard1/mongod.conf
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
StandardOutput=syslog
StandardError=syslog

[Install]
WantedBy=multi-user.target

systemctl daemon-reload
systemctl enable mongod-shard1

### 配置shard2

touch /data/mongodb/shard2/mongod.conf
添加配置:
systemLog:
    destination: file
    logAppend: true
    path: /data/mongodb/shard2/log/mongod.log
storage:
    dbPath: /data/mongodb/shard2/data
    journal:
        enabled: true
processManagement:
    fork: true
    pidFilePath: /data/mongodb/shard2/run/mongod.pid
net:
    port: 27002
replication:
    replSetName: shard2
sharding:
    clusterRole: shardsvr

touch /usr/lib/systemd/system/mongod-shard2.service
添加配置:
[Unit]
Description=MongoDB Database shard2 Service
Wants=network.target
After=network.target

[Service]
Type=forking
PIDFile=/data/mongodb/shard2/run/mongod.pid
ExecStart=/usr/mongodb-5.0.9/bin/mongod -f /data/mongodb/shard2/mongod.conf
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
StandardOutput=syslog
StandardError=syslog

[Install]
WantedBy=multi-user.target

systemctl daemon-reload
systemctl enable mongod-shard2

### 配置shard3

touch /data/mongodb/shard3/mongod.conf
添加配置:
systemLog:
    destination: file
    logAppend: true
    path: /data/mongodb/shard3/log/mongod.log
storage:
    dbPath: /data/mongodb/shard3/data
    journal:
        enabled: true
processManagement:
    fork: true
    pidFilePath: /data/mongodb/shard3/run/mongod.pid
net:
    port: 27003
replication:
    replSetName: shard3
sharding:
    clusterRole: shardsvr

touch /usr/lib/systemd/system/mongod-shard3.service
添加配置:
[Unit]
Description=MongoDB Database shard3 Service
Wants=network.target
After=network.target

[Service]
Type=forking
PIDFile=/data/mongodb/shard3/run/mongod.pid
ExecStart=/usr/mongodb-5.0.9/bin/mongod -f /data/mongodb/shard3/mongod.conf
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
StandardOutput=syslog
StandardError=syslog

[Install]
WantedBy=multi-user.target

systemctl daemon-reload
systemctl enable mongod-shard3

## 启动服务

systemctl start mongod-config
systemctl start mongod-shard1
systemctl start mongod-shard2
systemctl start mongod-shard3
systemctl start mongos

## 添加配置节点

/usr/mongodb-5.0.9/bin/mongo --port 27100
config = { _id: "config", members:[{_id:0,host:"*************:27100"}, {_id:1,host:"*************:27100"}, {_id:2,host:"*************:27100"}]}
rs.initiate(config)

## 添加shard1节点

进入服务器1
/usr/mongodb-5.0.9/bin/mongo --port 27001
config = { _id : "shard1", members : [ {_id : 0, host : "*************:27001" }, {_id : 1, host : "*************:27001" }, {_id : 2, host : "*************:27001", arbiterOnly: true }]}
rs.initiate(config)

## 添加shard2节点

进入服务器2
/usr/mongodb-5.0.9/bin/mongo --port 27002
config = { _id : "shard2", members : [ {_id : 0, host : "*************:27002" }, {_id : 1, host : "*************:27002", arbiterOnly: true }, {_id : 2, host : "*************:27002" }]}
rs.initiate(config)

## 添加shard3节点

进入服务器3
/usr/mongodb-5.0.9/bin/mongo --port 27003
config = { _id : "shard3", members : [ {_id : 0, host : "*************:27003" , arbiterOnly: true }, {_id : 1, host : "*************:27003" }, {_id : 2, host : "*************:27003" }]}
rs.initiate(config)

## 注册所有节点

sh.addShard("shard1/*************:27001,*************:27001,*************:27001")
sh.addShard("shard2/*************:27002,*************:27002,*************:27002")
sh.addShard("shard3/*************:27003,*************:27003,*************:27003")

## 创建用户

/usr/mongodb-5.0.9/bin/mongo --port 27017
db.createUser({user:'root', 'pwd':'smartpthdata','roles':[{role:'root',db:'admin'}]})

## 设置只能账户登录

openssl rand -base64 756 > /data/mongodb/config/mongo.key
chmod 400 /data/mongodb/config/mongo.key
每个config节点和shard节点的mongod.conf分别添加
security:
    keyFile: "/data/mongodb/config/mongo.key"
    authorization: "enabled"
每个mongos服务的mongod.config分别添加
security:
    keyFile: "/data/mongodb/config/mongo.key"

## 重启所有节点

### 关闭服务

systemctl stop mongod-config
systemctl stop mongod-shard1
systemctl stop mongod-shard2
systemctl stop mongod-shard3
systemctl stop mongos

### 启动服务

systemctl start mongod-config
systemctl start mongod-shard1
systemctl start mongod-shard2
systemctl start mongod-shard3
systemctl start mongos

### 创建分片数据库

use test
sh.enableSharding("test")
sh.shardCollection("test.col1", { "name" : 1 } )

## 常用维护命令

### 服务查看

ps -aux | grep "mongo"

### 关闭单个服务

kill -2 [pid]

### 添加节点

rs.add("[ip:port]")

### 添加仲裁节点

rs.addArb("[ip:port]")

### 删除节点

rs.remove("[ip:port]")

### 查看节点状态

rs.status()
