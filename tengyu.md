# 腾御
## wifi
www.119.net
## 二级测试
### 地址
https://demosub.119.net
### 账号
13776083430
### 密码
wh123456&
## 二级正式
### 地址
https://b.119.net/
### 账号
13776083430
### 密码
wh123456&
## Kuboard
### 地址
http://**************:30080
### 账号
admin
### 密码
iW9^#5c9VgJS
## rabbit-mq测试
### 地址
http://dev-rabbitmq.119.net
### 账号
sub119
### 密码
50nh0u5ZWBDM
## 二级跳板机
### 地址
************
### 账号
relay
### 密码
HHc8u74U$gMptj
## 一级平台(黄埔)
### 黄埔发布服务器
#### ip
************
#### 账号
root
#### 密码
E*![/x6ZfJ5az
#### 发布路径
/home/<USER>/fastnode
#### coding项目地址
https://baigong.coding.net/p/fastNode
#### 发布方式
git pull origin master
使用 pm2 部署   pm2 restart 程序编号
#### 前端发布路径
app可能发布地址 /home/<USER>/www/hp.119.net/app
app可能发布地址 /home/<USER>/www/hp.119.net/bgapp
企业微信项目发布地址 /home/<USER>/www/hp.119.net/corp
大数据平台发布地址 /home/<USER>/www/hp.119.net/manage
#### 正式网址
https://hp.119.net
##### 账号
13776083431
##### 密码
wh147963AHH&
#### 测试网址
https://demo.119.net
##### 账号
13776083431
##### 密码
wh147963AHH&
#### 代码分支
1. 一级平台最新代码（合并后代码） master
2. 原黄浦支队项目代码 master
3. 原静安支队项目代码 ja-lv1
4. 原黄浦教育局项目代码 hp-jiaoyuju 自动发布
5. 原松江支队项目代码 sj-lv1 
6. 原嘉定支队项目代码 jd-lv1
7. 张江项目代码（2019老代码） zj-lv1
8. 一级Demo代码 tengyu-lv1-demo 最新松江和嘉定在这个分支上, 手动发布， 通过构建计划的test发布，目标为tengyu-lv1-demo，设置DOCKER_SET_NAME，松江为sj-lv1，嘉定为jd-lv1
#### mysql自动备份
https://github.com/gobackup/gobackup
#### 数据归集
https://qgei36kp2i.feishu.cn/file/boxcnJ3lSqPvvoP7v8KYuZ14X1w
#### 归集项目地址
服务器: ***********
应用名: pushtask
服务器目录地址: /data/task/pushtask
#### 黄浦支队维护电脑密码
963852
#### 联系人
1. 黄处 18117062308

### 张江服务器
#### ip
*************
#### 路径
/home/<USER>

### 二级demo
#### ip
*************
*************
#### 账号
ubuntu
#### 密码
HL6TewL$Qn7^#x