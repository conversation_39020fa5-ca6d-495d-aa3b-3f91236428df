## 创建用户的表空间

```sql
CREATE TABLESPACE open_user datafile '/data/dm8/dmdata/open_user/open_user.DBF' SIZE 512;
```

## 创建用户

```sql
create user "open_user" identified by "sj_axQjw001" default tablespace "open_user" default index tablespace "open_user";
```

## 修改密码

```sql
alter user open_user identified by "sj_OxQjw001";
```


## 授权用户表权限只读

```sql
grant select on GORMDEMO.sj_companies to open_user;
grant select on GORMDEMO.sj_buildings to open_user;
grant select on GORMDEMO.sj_safety_assessment_reports to open_user;
```

## 查看用户表权限
```sql
SELECT * FROM session_privs;
SELECT * FROM dba_sys_privs where grantee='open_user';
```

## 取消用户表权限
```sql
REVOKE DBA FROM open_user;
```