##  mysql客户端离线安装

- [mysql客户端离线安装](#mysql客户端离线安装)
- [1. 下载mysql deb budnle包](#1-下载mysql-deb-budnle包)
- [2. 安装mysql客户端](#2-安装mysql客户端)
- [3. 配置mysql客户端](#3-配置mysql客户端)
- [4. 测试mysql客户端](#4-测试mysql客户端)

## 1. 下载mysql deb budnle包
- 下载地址：https://dev.mysql.com/downloads/mysql/
- 选择对应的版本下载如：mysql-server_8.2.0-1ubuntu22.04_amd64.deb-bundle.tar
- 下载完成后解压到指定目录
    ```shell
    tar -xvf mysql-community-client-plugins_8.0.28-1ubuntu22.04_amd64.deb-bundle.tar
    ```
- 解压完成后mysql客户端安装包已经准备好
- 若系统缺少依赖包，需要下载依赖包，可在https://pkgs.org/ 搜索获取    
    - libsasl2-2 dfsg2
    - libsasl2-modules-db dfsg2
## 2. 安装mysql客户端
- 安装依赖包
    ```shell
    dpkg -i libsasl2-modules-db_2.1.27+dfsg2-3ubuntu1_amd64.deb
    ```
    ```shell
    dpkg -i libsasl2-2_2.1.27+dfsg2-3ubuntu1_amd64.deb
    ```
- 安装mysql客户端
  - 进入mysql解压目录
  - 执行安装命令
    ```shell
    dpkg -i mysql-common_8.2.0-1ubuntu22.04_amd64.deb
    ```
    ```shell
    dpkg -i mysql-community-client-plugins_8.2.0-1ubuntu22.04_amd64.deb
    ```
    ```shell
    dpkg -i mysql-community-client-core_8.2.0-1ubuntu22.04_amd64.deb
    ```
    ```shell
    dpkg -i libmysqlclient22_8.2.0-1ubuntu22.04_amd64.deb
    ```
    ```shell
    dpkg -i libmysqlclient-dev_8.2.0-1ubuntu22.04_amd64.deb
    ```
    ```shell
    dpkg -i mysql-community-client_8.2.0-1ubuntu22.04_amd64.deb 
    ```
    ```shell
    dpkg -i mysql-client_8.2.0-1ubuntu22.04_amd64.deb
    ```
- 安装完成后mysql客户端已经安装完成
## 3. 配置mysql客户端
- 配置mysql客户端连接信息
  - 在mysql解压目录下创建my.cnf文件
  - my.cnf文件内容如下：
    ```shell
    [client]
    host=
    port=
    user=
    password=
    ```
    - 配置完成后可以直接使用mysql命令连接数据库
        ```shell
        mysql -h host -P port -u user -p
        ```
## 4. 测试mysql客户端
- 使用mysql命令连接数据库
- 连接成功后可以执行sql语句
- 测试完成后mysql客户端安装完成