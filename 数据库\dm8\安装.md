## 检查系统选择对应版本的安装包

1. 获取系统位数
    
    ```shell
    getconf LONG_BIT
    ```

2. 查看操作系统release信息
        
    ```shell
    cat /etc/system-release
    ```
3. 查看系统名称
   
    ```shell
    uname -a
    ```

4. 查看cpu信息

    ```shell
    lscpu
    ```
## 创建安装用户

```shell
groupadd -g 12349 dinstall //创建用户组
useradd -u 12345 -g dinstall -m -d /home/<USER>/bin/bash dmdba //创建用户
passwd dmdba //设置密码 Qaz@2024!
```

## 创建安装路径
    
```shell
mkdir /data/dmdba/dmdbms
chmod 777 /data/dmdba/dmdbms
chown -R dmdba:dinstall /data/dmdba
```


## 解压安装包

```shell
unzip dm8_20241226_HG_kylin10_64.zip
```

## 挂载镜像
    
```shell
mkdir /data/dm8iso
mount dm8_20241226_HG_kylin10_64.iso /data/dm8iso/
```

## 设置DMInstall权限
    
```shell
chmod 777 /data/dm8iso/DMInstall.bin
```
## 切换为dmdba用户

```shell
su dmdba
```

## 运行安装程序

```shell
/data/dm8iso/DMInstall.bin -i
```
## 切换root账号
    
 ```shell
 su root
 ```
## 执行root_installer.sh脚本

```shell
sh /data/dm8/dmdba/dmdbms/scripts/root/root_installer.sh
```

## 切换为dmdba用户

```shell
su dmdba
```

## 初始化数据库

```shell
/data/dm8/dmdba/dmdbms/bin/dminit PATH=/data/dm8/dmdata LOG_PATH=/data/dm8/logs/dmdata01.log LOG_PATH=/data/dm8/logs/dmdata02.log PAGE_SIZE=16 CHARSET=1 SYSDBA_PWD=sj_axQjw001 SYSAUDITOR_PWD=sj_axQjw001 DB_NAME=DM INSTANCE_NAME=DMSERVER PORT_NUM=5236 CASE_SENSITIVE=0 LOG_SIZE=1024
```

## 切换root账号
    
 ```shell
 su root
 ```

## 注册数据库服务

```shell
/data/dm8/dmdba/dmdbms/scripts/root/dm_service_installer.sh -t dmserver -dm_ini /data/dm8/dmdata/DM/dm.ini -p DMSERVER
/data/dm8/dmdba/dmdbms/scripts/root/dm_service_installer.sh -s /data/dm8/dmdba/dmdbms/bin/DmServiceDMSERVER
```

## 启动数据库

```shell
systemctl enable DmServiceDMSERVER
systemctl start DmServiceDMSERVER
```