## k8s

### k8s有哪些配置

1. Pod配置：Pod是Kubernetes中最小的部署单位，包含一个或多个相关容器。Pod配置可以指定容器的镜像、资源限制、环境变量以及卷等。

2. Deployment配置：Deployment用于定义应用程序的期望状态，并自动创建和管理Pod副本。它可以指定副本数、更新策略、滚动升级等。

3. Service配置：Service用于将一组Pod暴露给其他服务或外部网络。Service配置可以定义负载均衡类型、端口映射、服务发现方式等。

4. Ingress配置：Ingress是Kubernetes中的入口控制器，用于将外部流量路由到集群内的Service。Ingress配置可以定义域名规则、TLS证书、转发规则等。

5. ConfigMap和Secret配置：ConfigMap用于存储应用程序的配置数据，可以通过挂载成文件或环境变量的形式注入到容器中。Secret用于存储敏感信息，如密码、API密钥等。

6. PersistentVolume和PersistentVolumeClaim配置：PersistentVolume是用于持久化存储的抽象，可以将存储资源（如磁盘、网络存储等）动态地分配给Pod。PersistentVolumeClaim用于声明对PersistentVolume的需求和访问策略。

7. Namespace配置：Namespace用于将集群划分为多个虚拟环境，每个环境拥有独立的资源和权限。Namespace配置可以用于隔离和管理多个应用程序、团队或环境。