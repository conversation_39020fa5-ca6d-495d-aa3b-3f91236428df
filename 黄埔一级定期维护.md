# 运维管理

## mysql登录

```bash
mysql -u 用户名 -p
```

## 远程mysql登录

```bash
mysql -h 服务器地址 -P 端口 -u 用户名 -p
```

## 查看mysql数据库大小

```sql
SELECT table_schema AS 'Database', 
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' 
    FROM information_schema.TABLES 
    GROUP BY table_schema;
```

## 查看mysql数据库列表

```sql
SHOW DATABASES;
```

## 查看mysql表列表

```sql
SHOW TABLES;
```

## 查看mysql数据存放地址

```sql
SHOW VARIABLES LIKE 'datadir';
```

## 查看mysql内存占用

```sql
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
```

## 备份mysql数据库

```bash
mysqldump -u 用户名 -p 数据库名 > 备份文件名
```

## 查看文件夹大小

```bash
du -sh 文件夹名
```

## 查看磁盘空间

```bash
df -h
```

## 查看内存

```bash
free -h
```

## 查看cpu

```bash
top
```

## 查看进程

```bash
## 详细版
ps -aux | head -n 1 && ps -aux | grep 进程名
## 精简版
ps -a | head -n 1 && ps -a | grep 进程名
```

## 实时查看进程

```bash
top  -p $(pgrep -d',' -f 进程名)
```

## mongodb登录

```bash
mongo -u 用户名 -p 密码 --authenticationDatabase 授权数据库
```

## mongodb查看磁盘占用

```mongo
db.stats(1024 * 1024)
```

## mongodb查看内存占用

```mongo
db.serverStatus().mem
```

## mongodb查看数据存储路径

```mongo
db.adminCommand({ getParameter: 1, storage: 1 })
```

## rabbitmq查看状态

```bash
rabbitmqctl status
```

## rabbitmq查看队列

```bash
rabbitmqctl list_queues
```

## rabbitmq查看交换机

```bash
rabbitmqctl list_exchanges
```

## rabbitmq查看绑定

```bash
rabbitmqctl list_bindings
```

## rabbitmq查看内存占用

```bash
rabbitmqctl status | grep -A 3 memory
```

## rabbitmq查看磁盘占用

```bash
rabbitmqctl status | grep -A 3 disk
```

## rabbitmq查看安装路径

```bash
rabbitmqctl status | grep -A 3 db_dir
```

## redis登录

```bash
redis-cli -h 服务器地址 -p 端口 -a 密码
```

## redis查看内存占用

```bash
redis-cli -h 服务器地址 -p 端口 -a 密码 info memory
```

## redis查看磁盘占用

```bash
redis-cli -h 服务器地址 -p 端口 -a 密码 info persistence
```

## redis查看数据存放路径

```bash
redis-cli -h 服务器地址 -p 端口 -a 密码 config get dir
```

# 系统和网络维护

## 检查系统帐户，确保无异常用户

```bash
cat /etc/passwd
```

## 检查用户登陆日志

```bash
last
```

## 检查系统定时任务（计划任务）

```bash
crontab -l
```

## 检查系统定时任务执行情况

```bash
cat /var/log/cron
```

## 核对系统日期和时间

```bash
date
```

## 查看系统运行时间

```bash
uptime
```

## 检查是否存在异常进程

```bash
ps -ef
```

## 检查CPU运行负载（占用率）

```bash
top
```

## 检查内存运行负载（总量、空间）

```bash
free -m
```

## 检查分区挂载及分区使用情况

```bash
df -h
```

## 查看磁盘剩余空间（低于90%要预警）

```bash
df -h
```

## 检查系统高危漏洞情况

```bash
rpm -qa
```

## 检查网络配置

```bash
ifconfig
```

## 检查网络上下行流量

```bash
netstat -i
```

## 检查已开放端口是否符合要求

```bash
netstat -tunlp
```

## 检查系统防火墙规则

```bash
iptables -L
```

## 检查云平台安全组配置

```bash
iptables -L
```

## 检查云平台报警历史

```bash
iptables -L
```

## 调优云平台报警配置

```bash
iptables -L
```

# 数据库维护

## MYSQL ***********

### 登录

```bash
mysql -h *********** -P 8306 -u root -p
```

输入密码: goo.Happy123

### 检查数据库存储空间

```sql
SELECT table_schema AS 'Database', 
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' 
    FROM information_schema.TABLES 
    GROUP BY table_schema;
```

### 对比数据库周期增长情况

```sql
SELECT table_schema AS 'Database', 
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' 
    FROM information_schema.TABLES 
    GROUP BY table_schema;
```

### 数据库备份

```bash
mysqldump -u root -p hp_net119 > hp_net119_yyyy_mm_dd
```

### 数据库恢复测试（有效性）

```bash
mysqldump -h
```

### 清理无意义库表记录

```bash
mysqldump -h
```

### 删除临时备份表

```bash
mysqldump -h
```

### 检查数据库日志

```sql
SHOW VARIABLES LIKE 'log_error';
```

## SQL语句分析及优化

### 检查慢查询日志

```sql
SHOW VARIABLES LIKE 'slow_query_log';
```

## 检查数据库进程的CPU和内存使用率

```sql
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
```

### 关注高危漏洞情况

```sql
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
```

### 配置优化或升级

```sql
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
```

## mongodb 173.82.77.4

### 登录

```bash
mongo -u hp_net119 -p qwer_asdf --authenticationDatabase hp_net119
```

### 检查数据库存储空间

```mongo
db.stats(1024 * 1024)
```

### 对比数据库周期增长情况

```mongo
db.stats(1024 * 1024)
```

### 数据库备份

```bash
mongodump -h
```

### 数据库恢复测试（有效性）

```bash
mongodump -h
```

### 清理无意义库表记录

```bash
mongodump -h
```

### 删除临时备份表

```bash
mongodump -h
```

### 检查数据库日志

```mongo
db.adminCommand({ getParameter: 1, logComponentVerbosity: 1 })
```

### SQL语句分析及优化

```mongo
db.adminCommand({ getParameter: 1, logComponentVerbosity: 1 })
```

### 检查数据库进程的CPU和内存使用率

```mongo
db.serverStatus().mem
```

### 关注高危漏洞情况

```mongo
db.serverStatus().mem
```

### 配置优化或升级

```mongo
db.serverStatus().mem
```

# 中间件维护

## nginx 173.82.77.3

### 地址

which nginx

### 负载分析

```bash
netstat -an | grep 80 | wc -l
```

### 日志分析

```bash
ls logs
```

### 关注高危漏洞情况

```bash
ls logs
```

### 配置优化或升级

```bash
ls logs
```

## redis 173.82.77.5

### 登录

```bash
redis-cli -h 173.82.77.5 -p 6379 -a happy123
```

### 负载分析

```bash
netstat -an | grep 6379 | wc -l
```

```bash
redis-cli -h 173.82.77.5 -p 6379 -a happy123 info memory
```

```bash
redis-cli -h 173.82.77.5 -p 6379 -a happy123 info persistence
```

```bash
redis-cli -h 173.82.77.5 -p 6379 -a happy123 config get dir
```

### 日志分析

```bash
sudo tail -f /var/log/redis/redis-server.log
```

### 关注高危漏洞情况

```bash
ls logs
```

### 配置优化或升级

```bash
ls logs
```

## rabbitmq 173.82.77.1

### 负载分析

```bash
rabbitmqctl status
```

```bash
rabbitmqctl status | grep -A 3 memory
```

```bash
rabbitmqctl status | grep -A 3 disk
```

### 日志分析

```bash
rabbitmqctl status | grep -A 3 db_dir
```

```bash
tail -f /var/log/rabbitmq/<EMAIL>
```

### 关注高危漏洞情况

```bash
ls logs
```

### 配置优化或升级

```bash
ls logs
```

## PM2

### 负载分析

```bash
pm2 list
```

### 日志分析

```bash
pm2 logs
```

### 关注高危漏洞情况

```bash
ls logs
```

### 配置优化或升级

```bash
ls logs
```

https://github.com/gobackup/gobackup
