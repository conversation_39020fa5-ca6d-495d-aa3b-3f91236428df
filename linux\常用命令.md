## 查询端口占用
```shell
netstat -tnlp | grep :80
```
```
lsof -i:80
```

## 查看操作系统
```shell
cat /etc/os-release
```
```shell
uname -a
```
```shell
cat /etc/centos-release
```

## 压缩文件
```shell
zip -r archive.zip Documents/
```

## 查找文件名
```shell
find . -name "file*"
```

## 查找文件名根据更完整的路径
```shell
find . -wholename "*/dir1/file*"
```

## 查找文件内容并输出文件名和行号
```shell
grep -Hnr  "访问次数" . 
```

```shell
grep -Hnr "访问次数" --exclude-dir=dir1 --exclude-dir=dir2 .
```

## 查找文件内容并输出文件名
```shell
grep -lr "访问次数" .
```

## 匹配替换文件夹下所有文件中的字符串
```shell
find . -type f -exec sed -i 's/替换old/替换new/g' {} +
```
## tar打包
```shell
tar -zcvf archive.tar.gz Documents/
```

## tar解包
```shell
tar -zxvf archive.tar.gz -C dir
```

## xz解压
```shell
xz -d archive.tar.xz
```

## .tar解压
```shell
tar -xvf archive.tar
```


## 清理系统
1 清理系统日志
```shell
journalctl --disk-usage
journalctl --vacuum-time=1d
```

2 清理软件包
```shell
sudo apt-get autoremove --purge
sudo apt-get clean
sudo apt-get autoclean
```

3 清理docker
```shell
docker system prune -a
```

## ssh连接
```
ssh user@host
```

```shell
ssh -i ~/.ssh/id_rsa user@host
```

## ssh端口转发 正向隧道
```shell
ssh -L 8080:localhost:80 user@host
```

## ssh端口转发 反向隧道
```shell
ssh -R 8080:localhost:80 user@host
```

## scp文件传输
```shell
scp file.txt user@host:/remote/directory
```

## scp文件夹传输
```shell
scp -r dir user@host:/remote/directory
```

## 查看指定地址指定端口是否连通
```shell
nc -zv *********** 80
```
```shell
telnet *********** 80
```