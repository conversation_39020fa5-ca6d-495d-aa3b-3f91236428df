## 计算机位运算

位运算一般以字节为单位进行操作，其中低位在右，高位在左。例如，一个字节的二进制表示为 0011 1100，其中低位为0，高位为1。
之所以做从右到左的编号，是因为在计算机中，从右边开始，每个位的值都是2的幂，即第0位的值为1，第1位的值为2，第2位的值为4, 更容易计算。

### 与运算 &

描述: 对两个操作数的每个对应位执行AND操作，只有当两个位都为1时结果才为1
```c
#include <stdio.h>

int main()
{
    int a = 60; // 0011 1100
    int b = 13; // 0000 1101
    int c = 0;

    c = a & b; // 0000 1100
    printf("Line 1 - c 的值是 %d\n", c);

    return 0;
}
```
用途: 
1. 清零操作：通过与0进行与运算，可以将一个数据的指定位清零。例如，要将一个字节的最后一位清零，可以使用 data = data & 0xFE。
2. 取一个数中指定位：通过与1进行与运算，可以取一个数据的指定位。例如，要取data=01101101的第5位的值，可以使用 data = data & 0x10，这样data的值就是00010000，再右移4位，就可以得到00000001，即第5位的值。具体为 data = (data & 0x10) >> 4。
3. 判断奇偶性: 通过与1进行与运算，可以判断一个整数的最低位是0还是1, 从而判断其奇偶性。如果结果为0，则表示偶数；如果结果为1，则表示奇数。
4. 权限控制: 与运算可以用于对权限进行操作。通过将每个权限用一个二进制位表示，可以根据用户所具有的权限和需要的权限进行与运算，判断用户是否具备某种权限。
5. 位掩码: 与运算可以配合使用位掩码来进行位操作。位掩码是一个二进制位序列，用来提取或设置一个数据中的特定位。通过与运算，可以根据位掩码提取出特定的位，或者将特定的位设置为指定的值。和取一个数中指定位的方法类似。

### 或运算 |

描述: 对两个操作数的每个对应位执行OR操作，只要两个位中有一个为1时，结果就为1
```c
#include <stdio.h>

int main()
{
    int a = 60; // 0011 1100
    int b = 13; // 0000 1101
    int c = 0;

    c = a | b; // 0011 1101
    printf("Line 1 - c 的值是 %d\n", c);

    return 0;
}
```
用途: 

1. 设置特定位: 可以使用或运算将特定位置设为1。例如将data=00001001的第5位设为1，可以使用 data = data | 0x10。
2. 合并标志位: 可以使用或运算将多个标志位合并成一个。 例如，data1=0b001,data2=0b010,data3=0b100，可以使用 data = data1 | data2 | data3，将data的值设置为0b111。
3. 判断某一位是否为1：可以使用或运算来判断一个数的特定位是否为1。例如，可以将该数与一个只有特定位为1的数进行或运算，并通过结果是否改变来判断特定位是否为1。例如，要判断data=01101101的第5位是否为1，可以使用 data = data | 0x10，如果data的值改变了，则表示第5位为1，否则第5位为0。

### 非运算 ~

描述: 对操作数的每个位执行NOT操作，将1变为0，将0变为1

```c
#include <stdio.h>

int main()
{
    int a = 60; // 0011 1100
    int c = 0;

    c = ~a; // 1100 0011
    printf("Line 1 - c 的值是 %d\n", c);

    return 0;
}
```
用途:

1. 取反：可以使用非运算将一个数的所有位取反。例如，如果想将一个数的所有位取反，可以直接对该数进行非运算。例如，要将data=01101101的所有位取反，可以使用 data = ~data，这样data的值就是10010010。
2. 清零特定位：可以使用非运算将一个数的特定位清零。方法是先使用非运算将特定位取反（变为1），再与原数进行与运算。例如, 要将data=0b00001001的第4位清零，可以先将mask=0b00001000取反~mask，然后使用 data = data & mask，这样data的值就是0b00000001。
3. 提取补码: 可以使用非运算来提取一个数的补码。补码是计算机中表示负数的一种方式，如果数据类型是有符号类型，那么最高位为1时，表示负数，最高位为0时，表示正数。正数的补码就是其本身，负数的补码是其反码加1。例如，3的二进制是00000011，通过补码，data = ~data + 1，就可以得到-3的二进制表示为11111101。通过对-3提取补码，data = ~data + 1，就可以得到3的二进制表示为00000011。由此我们可以很容易的转换正负数的二进制表示。那么为甚么需要补码呢? 因为计算机中加法和减法的运算电路是一样的，所以计算机中减法运算就是加法运算。例如，计算3-2，可以转换为3+(-2)，即00000011+11111110，其结果为000000001, 计算机的加法运算超出的一位会被丢弃，所以结果就是1，即1的二进制表示。

### 异或运算 ^
描述: 对两个操作数的每个对应位执行XOR操作，当两个位不相同时结果为1
```c
#include <stdio.h>

int main()
{
    int a = 60; // 0011 1100
    int b = 13; // 0000 1101
    int c = 0;

    c = a ^ b; // 0011 0001
    printf("Line 1 - c 的值是 %d\n", c);

    return 0;
}
```

用途:

1. 交换变量的值: 可以使用异或运算来交换两个变量的值。例如，要交换a和b的值，可以使用 a = a ^ b，b = a ^ b，a = a ^ b，这样a的值就是b的值，b的值就是a的值。这种方法利用了异或运算的性质，相同数值异或的结果为0，任何数值与0异或的结果为自身。
2. 检测数字出现的次数: 异或运算还可以用于检测一个数字在数组中出现的次数。如果一个数字在数组中出现偶数次，那么所有的异或操作将得到0。只有当一个数字出现奇数次时，最终的结果才会是该数字本身。因此，通过对数组中的所有数字进行异或操作，最终得到的结果就是出现奇数次的数字。例如，数组[1,2,3,4,5,4,3,2,1]中，只有5出现了奇数次，其他数字都出现了偶数次，所以对数组中的所有数字进行异或操作，最终得到的结果就是5。
3. 校验数据的完整性: 在通信和存储中，数据的完整性是非常重要的。异或运算可以用来校验数据是否在传输过程中被篡改。发送方在发送数据之前，将数据的各个部分进行异或运算，并将结果作为校验值附加在数据后面。接收方在接收数据后，再次对数据部分进行异或运算，并将结果与校验值进行比较。如果两者相等，则说明数据没有被篡改。
4. 加密和解密: 异或运算也可以用于简单的加密和解密操作。通过将明文与密钥进行异或运算，可以得到密文。同样地，将密文与密钥进行异或运算，就可以得到原始的明文。这种加密方法称为异或加密，但它相对较弱，容易被破解，不适合用来保护敏感信息。

### 左移运算 <<

描述: 将操作数的每个位向左移动指定的位数，右边用0填充
```c
#include <stdio.h>

int main()
{
    int a = 60; // 0011 1100
    int c = 0;

    c = a << 2; // 1111 0000
    printf("Line 1 - c 的值是 %d\n", c);

    return 0;
}
```

用途:
### 右移运算 >>

描述: 将操作数的每个位向右移动指定的位数，左边用0填充
```c
#include <stdio.h>

int main()
{
    int a = 60; // 0011 1100
    int c = 0;

    c = a >> 2; // 0000 1111
    printf("Line 1 - c 的值是 %d\n", c);

    return 0;
}
```

用途: