## ClamAV安装
rpm -ivh --prefix=/usr/local/clamav clamav-1.0.0.linux.x86_64.rpm

## 创建目录和日志文件
mkdir -p /usr/local/clamav/logs
mkdir -p /usr/local/clamav/update
mkdir -p /usr/local/clamav/socket
touch /usr/local/clamav/logs/clamd.log
touch /usr/local/clamav/logs/freshclam.log

## 配置ClamAV
cp /usr/local/clamav/etc/clamd.conf.sample /usr/local/clamav/etc/clamd.conf
cp /usr/local/clamav/etc/freshclam.conf.sample /usr/local/clamav/etc/freshclam.conf

vi /usr/local/clamav/etc/clamd.conf
Example//注释掉这一行
#添加以下内容
LogFile /usr/local/clamav/logs/clamd.log
PidFile /usr/local/clamav/update/clamd.pid
DatabaseDirectory /usr/local/clamav/update
LocalSocket /usr/local/clamav/socket/clamd.socket

vi /usr/local/clamav/etc/freshclam.conf
Example//注释掉这一行
#添加以下内容
DatabaseDirectory /usr/local/clamav/update
UpdateLogFile /usr/local/clamav/logs/freshclam.log
PidFile /usr/local/clamav/update/freshclam.pid

复制下文件
cp /usr/local/clamav/etc/*.conf /usr/local/etc/

## 配置库文件

vi /etc/ld.so.conf
/usr/local/clamav/lib64
更新生效
ldconfig

## 创建clamav用户并授权
groupadd clamav
useradd -g clamav clamav
chown clamav.clamav /usr/local/clamav/logs/clamd.log
chown clamav.clamav /usr/local/clamav/logs/freshclam.log
chown clamav.clamav /usr/local/clamav/logs
chown clamav.clamav /usr/local/clamav/update/
chown clamav.clamav /usr/local/clamav/socket/

## 更新病毒库
cp /home/<USER>/bytecode.cvd /usr/local/clamav/update/
cp /home/<USER>/main.cvd /usr/local/clamav/update/
cp /home/<USER>/daily.cvd /usr/local/clamav/update/

## 创建软链接
ln -s /usr/local/clamav/bin/clamscan /usr/local/bin/clamscan
ln -s /usr/local/clamav/bin/freshclam /usr/local/bin/freshclam

## 启动clamav守护进程
freshclam --daemon
## 设置freshclam开机自启动
echo "/usr/local/clamav/bin/freshclam --daemon">>/etc/rc.d/rc.local
/usr/local/clamav/sbin/clamd

## 测试
clamscan -r .